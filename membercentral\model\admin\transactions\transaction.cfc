<cfcomponent output="no">
	<cffunction name="generateRefundStatement" access="public" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="tmpFolder" type="string" required="yes">
		<cfargument name="encryptFile" type="boolean" required="yes">
        <cfargument name="refundStatementDesc" type="string" required="no" default="">        

		<cfset var local = structnew()>
		<cfset local.strRefundStatement = { refundStatementPath='', refundStatementPayOnlineStr=structNew() }>

		<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
			<cfprocresult name="local.qryTransaction" resultset="1">
			<cfprocresult name="local.qryTypeInfo" resultset="2">
		</cfstoredproc>

		<!--- Name and address --->
		<cfset local.qryMember = application.objMember.getMemberInfo(memberid=local.qryTransaction.assignedTomemberID, orgID=local.qryTransaction.ownedByOrgID)>
		<cfset local.qryAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryTransaction.ownedByOrgID, memberid=local.qryTransaction.assignedTomemberID)>
		<cfset local.addressinfo = "#local.qryMember.prefix# #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix#<br/>">
		<cfif len(trim(local.qryMember.company))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryMember.company)#<br/>">
		</cfif>
		<cfif len(trim(local.qryAddress.attn))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.attn)#<br/>">
		</cfif>
		<cfif len(trim(local.qryAddress.address1))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address1)#<br/>">
		</cfif>
		<cfif len(trim(local.qryAddress.address2))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address2)#<br/>">
		</cfif>
		<cfif len(trim(local.qryAddress.address3))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address3)#<br/>">
		</cfif>
		<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.city)#, #trim(local.qryAddress.statename)# #trim(local.qryAddress.postalCode)#<br/>">
		<cfif local.qryAddress.countryID is not 1>
			<cfset local.addressinfo = "#local.addressinfo##local.qryAddress.country#<br/>">
		</cfif>

		<cfset local.haystack = local.addressinfo>
		<cfset local.needle = "<br/>">
		<cfset local.numLines = (len(local.haystack) - len(ReplaceNoCase(local.haystack,local.needle,'','ALL')))/len(local.needle)>
		<cfset local.margintop = "3.5">

		<!--- render refund --->
		<cfsavecontent variable="local.refundHeader">
			<cfset local.c = "text-align:center;">
			<cfset local.l = "text-align:left;">
			<cfset local.r = "text-align:right;">
			<cfset local.bt = "border-top:1px solid ##000;">
			<cfset local.bb = "border-bottom:1px solid ##000;">
			<cfset local.bl = "border-left:1px solid ##000;">
			<cfset local.br = "border-right:1px solid ##000;">
			<cfset local.address = "font-size:12px;font-family:verdana;font-weight:bold;line-height:16px;margin-left:100px;">
			<cfset local.refund1 = "font-family:verdana;font-size:16px;line-height:22px;font-weight:bold;letter-spacing:4px;">
			<cfset local.refund2 = "font-family:verdana;font-size:11.5px;line-height:16px;padding:2px 0;">
			<cfset local.refund2a = "font-family:verdana;font-size:11px;line-height:16px;padding:2px 0;">
			<cfset local.logo = "width:400px;height:150px;">
			<cfset local.refundbox = "margin-top:4px; text-align:center; ">
			<cfset local.infotbl = "margin-top:6px;">
			<cfset local.height = "180">
			<cfset local.width = "400">
			<cfset local.tdwidth = "310">

			<cfoutput>
				<div>
					<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<td height="#local.height#" valign="top">
								<div style="#local.logo#;">
									<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/refunds/#local.qryTypeInfo.profileID#.#local.qryTypeInfo.refundImageExt#")>
										<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/refunds/#local.qryTypeInfo.profileID#.#local.qryTypeInfo.refundImageExt#" width="#local.width#"  />
									</cfif>
								</div>
							</td>
							<td height="#local.height#" width="#local.tdwidth#" valign="top">
								<div style="#local.infotbl#">
									<table cellspacing="0" cellpadding="2" width="#local.tdwidth#">
									<tr>
										<td colspan="2" style="#local.c##local.bt##local.bl##local.br##local.refund1#">REFUND STATEMENT</td>
									</tr>
									<tr valign="bottom">
										<td style="#local.bl##local.r##local.refund2a#;" width="50%"><b>Account Number</b> &nbsp;</td>
										<td style="#local.br##local.refund2a#">#local.qryMember.membernumber#</td>
									</tr>
									<tr>
										<td style="#local.bl##local.bb##local.r##local.refund2#;" width="50%"><b>Amount Refunded</b> &nbsp;</td>
										<td style="#local.br##local.bb##local.refund2#">#dollarformat(local.qryTransaction.amount)#</td>
									</tr>
									</table>
								</div>
								<div style="#local.refundbox#">
									<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/refunds/refundstamp.png" width="187" />
								</div>
							</td>
						</tr>					
					</table>
					<div style="#local.address#">
						#local.addressinfo#<cfif local.numLines lt 8>#repeatString("<br/>",8-local.numLines)#</cfif>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.refundFooter">
			<cfoutput>
			<div style="font-family:verdana;font-size:9px;text-align:center;">
				Refund Statement Generated #dateFormat(now(),'m/dd/yyyy')#<!-- cpp -->
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.refundBody">
			<cfset local.sec1_th = "font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;background-color:##cecece;">
			<cfset local.sec1_td_sttl = "text-align:right;background-color:transparent; padding-top:8px;">
			<cfset local.sec1_td = "font-size:11px;font-family:verdana;line-height:12px;">
            <cfset local.refunddescmsg = "font-size:13px;font-family:verdana;line-height:13px;margin-top:5px;margin-bottom:10px;">
			<cfset local.r = "text-align:right;">
			<cfset local.l = "text-align:left;">
			<cfset local.p = "padding-bottom:6px;">
			<cfset local.ph = "height:6px;">
			<cfset local.bt = "border-top:1px solid ##666;">
			<cfset local.bb = "border-bottom:1px solid ##666;">
			<cfset local.bl = "border-left:1px solid ##666;">
			<cfset local.br = "border-right:1px solid ##666;">
			<cfoutput>
			<html>
				<head>
					<style type="text/css">
						html, body { width:8in; padding:0; margin: 0; }
					</style>
				</head>
				<body>
                    <cfif len(trim(arguments.refundStatementDesc))>
					    <p>#arguments.refundStatementDesc#</p>
                    </cfif>
					<div id="sec1">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th width="*" colspan="2" style="#local.sec1_th##local.bb##local.l#">&nbsp; Details of Refund</th>
							<th width="10" style="#local.sec1_th##local.bb#">&nbsp;</th>
							<th width="70" style="#local.sec1_th##local.bb##local.r#">Amount&nbsp;</th>
						</tr>
						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						<tr valign="top">
							<td width="2" style="#local.sec1_td##local.p#">&nbsp;</td>
							<td width="*" style="#local.sec1_td##local.p#">
								#htmlEditFormat(local.qryTransaction.detail)#<br/><br/>
								#DollarFormat(local.qryTransaction.amount)# #local.qryTransaction.type# on #dateformat(local.qryTransaction.transactionDate,'m/d/yyyy')# #timeformat(local.qryTransaction.transactionDate,'h:mm tt')#
							</td>
							<td width="10" style="#local.sec1_td##local.p#">&nbsp;</td>
							<td width="70" style="#local.sec1_td##local.r##local.p#">#DollarFormat(local.qryTransaction.amount)#</td>
						</tr>
						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						<tr>
							<td width="*" colspan="2" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">Total Refund:</td>
							<td width="10" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">&nbsp;</td>
							<td width="70" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">#DollarFormat(local.qryTransaction.amount)#<cfif local.qryTransaction.amount gte 0>&nbsp;</cfif></td>
						</tr>
						</table>
					</div>
					<br/>
				</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- create a PDF --->
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.refundHeader } >
		<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.refundFooter } >
		<cftry>
			<cfdocument filename="#arguments.tmpFolder#/un_refundstatement.pdf" pagetype="letter" margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25">
						<cfdocumentitem attributeCollection="#local.headercol#">
							#local.refundHeader#
						</cfdocumentitem>
						#local.refundBody#
						<cfdocumentitem attributeCollection="#local.footercol#">
							#replace(local.refundFooter,'<!-- cpp -->',' &bull; Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
						</cfdocumentitem>
					</cfdocumentsection>
				</cfoutput>
			</cfdocument>		
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<!--- File Name of Refund Statement --->
		<cfset local.refundFileNameNoExt = "Refund #local.qryMember.membernumber# #local.qryMember.firstname# #local.qryMember.lastname#">
		<cfset local.refundFileNameNoExt = replace(replace(rereplaceNoCase(local.refundFileNameNoExt,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL')>

		<cfif arguments.encryptFile>
			<cfset application.objCommon.encryptPDF("#arguments.tmpFolder#/un_refundstatement.pdf","#arguments.tmpFolder#/#local.refundFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		<cfelse>
			<cffile action="move" source="#arguments.tmpFolder#/un_refundstatement.pdf" destination="#arguments.tmpFolder#/#local.refundFileNameNoExt#.pdf">
		</cfif>

		<cfset local.strRefundStatement.folderPath = arguments.tmpFolder>
		<cfset local.strRefundStatement.fileName = "#local.refundFileNameNoExt#.pdf">
		<cfset local.strRefundStatement.refundStatementPath = "#local.strRefundStatement.folderPath#/#local.strRefundStatement.fileName#">
		<cfset local.strRefundStatement.displayName = local.strRefundStatement.fileName>

		<cfreturn local.strRefundStatement>
	</cffunction>

	<cffunction name="generatePaymentAllocationStatement" access="public" returntype="struct" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="tmpFolder" type="string" required="yes">
		<cfargument name="encryptFile" type="boolean" required="yes">
		<cfargument name="paymentAllocationStatementDesc" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.strPaymentAllocationStatement = structNew()>
		<cfset local.strPaymentAllocationStatement = { paymentAllocationStatementPath='', success=false}>
		<cfset local.c = "text-align:center;">
		<cfset local.l = "text-align:left;">
		<cfset local.r = "text-align:right;">
		<cfset local.bt = "border-top:1px solid ##000;">
		<cfset local.bb = "border-bottom:1px solid ##000;">
		<cfset local.bl = "border-left:1px solid ##000;">
		<cfset local.br = "border-right:1px solid ##000;">
		<cfset local.address = "font-size:12px;font-family:verdana;font-weight:bold;line-height:16px;">
		<cfset local.refund1 = "font-family:verdana;font-size:16px;line-height:22px;font-weight:bold;letter-spacing:4px;">
		<cfset local.refund2 = "font-family:verdana;font-size:11.5px;line-height:16px;padding:2px 0;">
		<cfset local.refund2a = "font-family:verdana;font-size:11px;line-height:16px;padding:2px 0;">
		<cfset local.logo = "width:400px;height:150px;">
		<cfset local.refundbox = "margin-top:4px; text-align:center; ">
		<cfset local.infotbl = "margin-top:6px;">
		<cfset local.height = "180">
		<cfset local.width = "400">
		<cfset local.tdwidth = "310">

		<!--- Get payment allocation statement data --->
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_getPaymentAllocationStatementData">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
				<cfprocresult name="local.qryPaymentDetails" resultset="1">
				<cfprocresult name="local.qryPaymentHistory" resultset="2">
				<cfprocresult name="local.qryCurrentAllocations" resultset="3">
			</cfstoredproc>
		<cfcatch type="Any"><cfdump var="#cfcatch#"><cfabort>
			<cfset local.strPaymentAllocationStatement.errorMessage = "Error retrieving payment data: #cfcatch.message#">
			<cfreturn local.strPaymentAllocationStatement>
		</cfcatch>
		</cftry>

		<cfif local.qryPaymentDetails.recordcount eq 0>
			<cfset local.strPaymentAllocationStatement.errorMessage = "Payment transaction not found">
			<cfreturn local.strPaymentAllocationStatement>
		</cfif>

		<!--- Get payment profile for image --->
		<cfquery name="local.qryProfile" datasource="#application.dsn.membercentral.dsn#">
			SELECT profileID, refundImageExt
			FROM dbo.mp_profiles
			WHERE profileID = <cfqueryparam value="#local.qryPaymentDetails.profileID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		
		<cfset local.addressinfo = "#local.qryPaymentDetails.prefix# #local.qryPaymentDetails.firstname# #local.qryPaymentDetails.middlename# #local.qryPaymentDetails.lastname# #local.qryPaymentDetails.suffix#<br/>">
		<cfif len(trim(local.qryPaymentDetails.company))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.company)#<br/>">
		</cfif>
		<cfif len(trim(local.qryPaymentDetails.attn))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.attn)#<br/>">
		</cfif>
		<cfif len(trim(local.qryPaymentDetails.address1))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.address1)#<br/>">
		</cfif>
		<cfif len(trim(local.qryPaymentDetails.address2))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.address2)#<br/>">
		</cfif>
		<cfif len(trim(local.qryPaymentDetails.address3))>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.address3)#<br/>">
		</cfif>
		<cfset local.addressinfo = "#local.addressinfo##trim(local.qryPaymentDetails.city)#, #trim(local.qryPaymentDetails.name)# #trim(local.qryPaymentDetails.postalCode)#<br/>">
		<cfif local.qryPaymentDetails.countryID is not 1>
			<cfset local.addressinfo = "#local.addressinfo##local.qryPaymentDetails.country#<br/>">
		</cfif>

		<cfset local.haystack = local.addressinfo>
		<cfset local.needle = "<br/>">
		<cfset local.numLines = (len(local.haystack) - len(ReplaceNoCase(local.haystack,local.needle,'','ALL')))/len(local.needle)>
		<cfset local.margintop = "3.5">
		
		<!--- Build header content --->
		<cfsavecontent variable="local.paymentHeader">
			<cfoutput>
			<table cellspacing="0" cellpadding="0" width="100%">
			<tr>
				<td height="#local.height#" valign="top">
					<div style="#local.logo#;">
						<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/refunds/#local.qryProfile.profileID#.#local.qryProfile.refundImageExt#")>
							<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/refunds/#local.qryProfile.profileID#.#local.qryProfile.refundImageExt#" width="#local.width#"  />
						</cfif>
					</div>
				</td>
				<td height="#local.height#" width="#local.tdwidth#" valign="top">
					<div style="#local.infotbl#">
						<table cellspacing="0" cellpadding="2" width="#local.tdwidth#">
						<tr>
							<td colspan="2" style="#local.c##local.bt##local.bl##local.br##local.refund1#">PAYMENT ALLOCATION STATEMENT</td>
						</tr>
						<tr valign="bottom">
							<td style="#local.bt##local.bl##local.r##local.refund2a#;" width="50%"><b>Account Number</b> &nbsp;</td>
							<td style="#local.bt##local.br##local.refund2a#">#local.qryPaymentDetails.membernumber#</td>
						</tr>
						<tr>
							<td style="#local.bl##local.bb##local.r##local.refund2#;" width="50%"><b>Payment Amount</b> &nbsp;</td>
							<td style="#local.br##local.bb##local.refund2#">#dollarformat(local.qryPaymentDetails.originalAmount)#</td>
						</tr>
						</table>
					</div>
				</td>
			</tr>
			</table>
			</cfoutput>
		</cfsavecontent>

		<!--- Build footer content --->
		<cfsavecontent variable="local.paymentFooter">
			<cfoutput>
			<div style="text-align:center; font-size:10px; color:##666;">
				Payment Allocation Statement Generated on #DateFormat(now(),"m/d/yyyy")# at #TimeFormat(now(),"h:mm tt")# CT<!-- cpp -->
			</div>
			</cfoutput>
		</cfsavecontent>

		<!--- Build main content --->
		<cfsavecontent variable="local.paymentBody">
			<cfset local.sec1_th = "background-color:##ddd; font-size:12px; font-weight:bold; padding:5px; font-family:verdana;">
			<cfset local.sec1_td = "font-size:12px; padding:3px; font-family:verdana;">
			<cfset local.bb = "border-bottom:1px solid ##666;">
			<cfset local.l = "border-left:1px solid ##666;">
			<cfset local.r = "border-right:1px solid ##666;">
			<cfset local.p = "padding:5px;">
			<cfset local.ph = "padding:0 5px;">
			<cfset local.br = "border-right:1px solid ##666;">
			<cfoutput>
			<html>
				<head>
					<style type="text/css">
						html, body { width:8in; padding:0; margin: 0; }
					</style>
				</head>
				<body>
					<!--- Member Information Section --->
					<div style="#local.address#">
						#local.addressinfo#<cfif local.numLines lt 8>#repeatString("<br/>",8-local.numLines)#</cfif>
					</div>
					<cfif len(trim(arguments.paymentAllocationStatementDesc))>
						<p>#arguments.paymentAllocationStatementDesc#</p>
					</cfif>

					<!--- Payment Details Section --->
					<div id="sec1">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th colspan="2" style="#local.sec1_th##local.bb##local.l#text-align: left;">&nbsp; Details of Payment Allocations</th>
						</tr>
						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						<tr valign="top">
							<td width="2" style="#local.sec1_td##local.p#">&nbsp;</td>
							<td style="#local.sec1_td##local.p#">
								<strong>#DollarFormat(local.qryPaymentDetails.originalAmount)# payment by #local.qryPaymentDetails.paymentDetail# #DateFormat(local.qryPaymentDetails.paymentDate,"m/d/yyyy")# #timeformat(local.qryPaymentDetails.paymentDate,'h:mm tt')#</strong>
							</td>
						</tr>

						<!--- Payment History (Voids, Refunds, Write-offs) --->
						<cfloop query="local.qryPaymentHistory">
							<tr valign="top">
								<td width="2" style="#local.sec1_td##local.p#">&nbsp;</td>
								<td style="font-size:11px; padding:5px; vertical-align:top; padding-left:15px; font-family:verdana;">
									#DollarFormat(abs(local.qryPaymentHistory.amount))#<cfif local.qryPaymentHistory.displayType NEQ 'Refund'> #local.qryPaymentHistory.displayType# </cfif> #local.qryPaymentHistory.detail# #DateFormat(local.qryPaymentHistory.transactionDate,"m/d/yyyy")# #timeformat(local.qryPaymentHistory.transactionDate,'h:mm tt')#
								</td>
							</tr>
						</cfloop>

						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						</table>
					</div>

					<!--- Allocation Summary --->
					<div style="margin-top:10px;">
						<div style="font-size:12px; margin-bottom:5px 0; padding-left:17px; padding-bottom:10px; font-family:verdana;">
							<strong>#DollarFormat(local.qryPaymentDetails.unallocatedAmount)# of this payment is unallocated and can be refunded.</strong>
						</div>
						
						<div style="font-size:12px; margin-top:5px; margin-bottom:5px 0;  padding-left:17px; font-family:verdana;">
							<strong>#DollarFormat(local.qryPaymentDetails.allocatedAmount)# of this payment is allocated<cfif local.qryPaymentDetails.allocatedAmount gt 0> to:<cfelse>.</cfif></strong>
						</div>
					</div>

					<!--- Current Allocations --->
					<cfif local.qryCurrentAllocations.recordcount gt 0>
						<div style="margin-top:5px;">
							<table cellspacing="0" cellpadding="0" width="100%" style="border-collapse:collapse;">
								<cfloop query="local.qryCurrentAllocations">
									<cfif local.qryCurrentAllocations.totalAllocatedToSale GT 0>
										<tr>
											<td width="2" style="#local.sec1_td#">&nbsp;</td>
											<td width="2" style="#local.sec1_td#">&nbsp;</td>
											<td style="font-size:11px; padding:5px 0 5px 5px; vertical-align:top; width:80px; font-family:verdana;">
												#DollarFormat(local.qryCurrentAllocations.totalAllocatedToSale)#  to
											</td>
											<td style="font-size:11px; padding: 5px 5px 5px 0; vertical-align:top; font-family:verdana;">
												<div style="font-weight:bold;">#local.qryCurrentAllocations.saleDetail#</div>
												<div>#local.qryCurrentAllocations.memberName# (#local.qryCurrentAllocations.memberNumber#)</div>
											</td>
										</tr>
									</cfif>
								</cfloop>
							</table>
						</div>
					</cfif>
				</body>
			</html>
			</cfoutput>
		</cfsavecontent>
		
		<!--- Create PDF --->
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.paymentHeader } >
		<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.paymentFooter } >
		<cftry>
			<cfdocument filename="#arguments.tmpFolder#/un_paymentallocationstatement.pdf" pagetype="letter" margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25">
						<cfdocumentitem attributeCollection="#local.headercol#">
							#local.paymentHeader#
						</cfdocumentitem>
						#local.paymentBody#
						<cfdocumentitem attributeCollection="#local.footercol#">
							#replace(local.paymentFooter,'<!-- cpp -->',' &bull; Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
						</cfdocumentitem>
					</cfdocumentsection>
				</cfoutput>
			</cfdocument>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.strPaymentAllocationStatement.errorMessage = "Error generating PDF: #cfcatch.message#">
			<cfreturn local.strPaymentAllocationStatement>
		</cfcatch>
		</cftry>

		<!--- File Name of Payment Allocation Statement --->
		<cfset local.paymentFileNameNoExt = "PaymentAllocation #local.qryPaymentDetails.memberNumber# #local.qryPaymentDetails.firstName# #local.qryPaymentDetails.lastName#">
		<cfset local.paymentFileNameNoExt = replace(replace(rereplaceNoCase(local.paymentFileNameNoExt,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL')>

		<!--- Encrypt file if requested --->
		<cfif arguments.encryptFile>
			<cfset application.objCommon.encryptPDF("#arguments.tmpFolder#/un_paymentallocationstatement.pdf","#arguments.tmpFolder#/#local.paymentFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
		</cfif>

		<cfset local.strPaymentAllocationStatement.folderPath = arguments.tmpFolder>
		<cfset local.strPaymentAllocationStatement.fileName = "#local.paymentFileNameNoExt#.pdf">
		<cfset local.strPaymentAllocationStatement.paymentAllocationStatementPath = "#local.strPaymentAllocationStatement.folderPath#/#local.strPaymentAllocationStatement.fileName#">
		<cfset local.strPaymentAllocationStatement.displayName = local.strPaymentAllocationStatement.fileName>
		<cfset local.strPaymentAllocationStatement.success = true>

		<cfreturn local.strPaymentAllocationStatement>
	</cffunction>
</cfcomponent>